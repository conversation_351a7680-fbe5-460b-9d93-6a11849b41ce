<template>
  <q-card class="property-card cursor-pointer" flat bordered @click="emitStart">
    <div
      class="property-image-container"
      :style="{
        backgroundImage: `url(${getPropertyMainImage(listing)})`,
      }"
    >
      <div class="property-content">
        <div class="property-overlay">
          <!-- Country Flag -->
          <div class="country-flag-container">
            <div class="country-flag">
              {{ getCountryFlag(listing.listing_details?.country_code) }}
            </div>
          </div>
          <!-- <q-chip
            color="accent"
            text-color="white"
            icon="visibility"
            size="sm"
            class="preview-chip"
          >
            Preview
          </q-chip>
          <div class="property-price-hint" v-if="listing.listing_details?.price_hint">
            <q-icon name="monetization_on" size="sm" class="q-mr-xs" />
            <span>{{ listing.listing_details.price_hint }}</span>
          </div> -->
        </div>

        <div class="property-info q-px-md q-pt-sm">
          <div class="property-title text-weight-bold q-mb-xs">
            {{ listing.listing_details?.title || 'Property Details' }}
          </div>
          <!-- <div class="property-address text-caption text-white q-mb-sm">
            <q-icon name="place" size="xs" class="q-mr-xs" />
            {{ getPropertyAddress(listing) }}
          </div> -->
          <div class="property-stats">
            <div class="stats-row">
              <div
                class="stat-item"
                v-if="listing.listing_details?.count_bedrooms"
              >
                <q-icon name="bed" size="sm" class="q-mr-xs" color="white" />
                <span>{{ listing.listing_details.count_bedrooms }} bed</span>
              </div>
              <div
                class="stat-item"
                v-if="listing.listing_details?.count_bathrooms"
              >
                <q-icon name="bathtub" size="sm" class="q-mr-xs" color="white" />
                <span>{{ listing.listing_details.count_bathrooms }} bath</span>
              </div>
              <div
                class="stat-item"
                v-if="listing.listing_details?.square_feet"
              >
                <q-icon name="square_foot" size="sm" class="q-mr-xs" color="white" />
                <span>{{ listing.listing_details.square_feet }} sqft</span>
              </div>
            </div>
          </div>
        </div>

        <q-card-actions class="q-pa-md q-pt-none">
          <q-btn
            color="primary"
            unelevated
            size="md"
            icon="quiz"
            label="Guess this property's price"
            class="full-width guess-button"
            no-caps
            @click.stop="emitStart"
          >
            <q-tooltip>Try guessing the price!</q-tooltip>
          </q-btn>
        </q-card-actions>
      </div>
      <div class="image-fallback" v-if="!getPropertyMainImage(listing)">
        <q-icon name="home" size="3.5em" color="grey-4" />
      </div>
    </div>
  </q-card>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
const props = defineProps({
  listing: { type: Object, required: true },
  getPropertyMainImage: { type: Function, required: true },
  getPropertyAddress: { type: Function, required: true },
})
const emit = defineEmits(['start'])
const emitStart = () => emit('start')

// Function to get country flag emoji
const getCountryFlag = (countryCode) => {
  const countryFlags = {
    'GB': '🇬🇧',
    'US': '🇺🇸',
    'CA': '🇨🇦',
    'AU': '🇦🇺',
    'DE': '🇩🇪',
    'FR': '🇫🇷',
    'ES': '🇪🇸',
    'IT': '🇮🇹',
    'NL': '🇳🇱',
    'BE': '🇧🇪',
    'IE': '🇮🇪',
    'PT': '🇵🇹',
    'CH': '🇨🇭',
    'AT': '🇦🇹',
    'SE': '🇸🇪',
    'NO': '🇳🇴',
    'DK': '🇩🇰',
    'FI': '🇫🇮',
    'PL': '🇵🇱',
    'CZ': '🇨🇿',
    'HU': '🇭🇺',
    'SK': '🇸🇰',
    'SI': '🇸🇮',
    'HR': '🇭🇷',
    'RO': '🇷🇴',
    'BG': '🇧🇬',
    'GR': '🇬🇷',
    'CY': '🇨🇾',
    'MT': '🇲🇹',
    'LU': '🇱🇺',
    'EE': '🇪🇪',
    'LV': '🇱🇻',
    'LT': '🇱🇹'
  }

  return countryFlags[countryCode] || '🌍' // World flag as default
}
</script>

<style scoped>
.property-card {
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  background: linear-gradient(145deg, #ffffff, #f8fafc);
}
.property-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  transform: translateY(-4px);
  border-color: #2563eb;
}
.property-image-container {
  position: relative;
  height: 300px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.property-image-container {
  /* This property is set by your inline style, but the rules below control it */
  /* background-image: url(...) */
  background-size: cover;      /* Makes the image cover the whole div */
  background-position: center; /* Centers the image within the div */
  background-repeat: no-repeat;  /* Ensures the image doesn't tile */
}
.property-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.property-overlay {
  position: absolute;
  top: 2px;
  right: 2px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-end;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0.2) 50%,
    transparent 100%
  );
  padding: 4px;
  border-radius: 12px;
}

.country-flag-container {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(6px);
  border-radius: 8px;
  padding: 4px 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.country-flag {
  font-size: 1.2rem;
  line-height: 1;
}
.preview-chip {
  backdrop-filter: blur(6px);
  background: rgba(37, 99, 235, 0.9);
  border-radius: 12px;
  padding: 4px 10px;
  font-weight: 500;
}
.property-price-hint {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  font-weight: 500;
}
.property-info {
  /* padding: 16px; */
  color: white;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0.2) 50%,
    transparent 100%
  );
}
.property-title {
  font-size: 1.2rem;
  line-height: 1.4;
  color: #ffffff;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}
.property-address {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  color: #f3f4f6;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}
.property-stats {
  margin-top: 12px;
}
.stats-row {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}
.stat-item {
  display: flex;
  align-items: center;
  font-size: 0.85rem;
  color: #f3f4f6;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}
.guess-button {
  border-radius: 8px;
  padding: 8px;
  font-weight: 600;
  background: linear-gradient(90deg, #2563eb, #3b82f6);
  transition: all 0.3s ease;
}
.guess-button:hover {
  background: linear-gradient(90deg, #1d4ed8, #2563eb);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}
.image-fallback {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e5e7eb;
}
@media (max-width: 768px) {
  .property-image-container {
    height: 280px;
  }
  .property-title {
    font-size: 1.1rem;
  }
  .guess-button {
    font-size: 0.9rem;
  }
}
@media (max-width: 480px) {
  .property-image-container {
    height: 260px;
  }
  .property-title {
    font-size: 1rem;
  }
  .guess-button {
    font-size: 0.85rem;
  }
}
</style>