<template>
  <div class="roundup-game-layout">
    <!-- Header -->
    <div class="round-up-header">
      <div class="round-up-header-max-ctr">
        <div class="row items-center justify-between q-py-lg">
          <div class="col-12">
            <div class="ru-game-layout-container">
              <!-- Welcome Section -->
              <div
                class="welcome-section text-center q-mb-md"
                :style="backgroundStyle"
                @click="goToRoundupGameStart"
                tabindex="0"
                @keydown.enter="goToRoundupGameStart"
                style="cursor: pointer"
              >
                <!-- Background overlay for better text readability -->
                <div class="game-overlay">
                  <div class="welcome-icon q-mb-lg">
                    <img
                      class="hpg-logo-img"
                      :src="logoUrl"
                      alt="HousePriceGuess Logo"
                    />
                  </div>
                  <h1 class="text-h3 text-weight-bold text-white q-mb-md">
                    {{ storeGameTitle }}
                  </h1>
                  <p class="text-h6 text-white q-mb-lg q-pb-lg">
                    Test your property valuation skills with real properties
                  </p>
                  <!-- <div class="challenge-stats">
                    <div class="row q-col-gutter-md justify-center">
                      <div class="col-auto">
                        <q-chip color="primary" text-color="white" icon="home">
                          {{ storeTotalProperties }} Properties
                        </q-chip>
                      </div>
                      <div class="col-auto">
                        <q-chip
                          color="secondary"
                          text-color="white"
                          icon="timer"
                        >
                          ~{{ Math.ceil(storeTotalProperties * 0.5) }} minutes
                        </q-chip>
                      </div>
                      <div class="col-auto">
                        <q-chip
                          color="positive"
                          text-color="white"
                          icon="emoji_events"
                        >
                          Score up to {{ storeTotalProperties * 100 }} points
                        </q-chip>
                      </div>
                    </div>
                  </div> -->
                </div>
              </div>

              <!-- -->
            </div>
            <!-- <div>
              <h1 class="text-h4 text-weight-bold text-primary q-mb-sm">
                <router-link
                  :to="{
                    name: 'rRoundupGameStart',
                    params: { gameSlug: $route.params.gameSlug },
                  }"
                >
                  {{ gameTitle }}
                </router-link>
                ...
              </h1>
            </div> -->
          </div>
          <div class="header-right">
            <div class="row items-center q-gutter-sm">
              <!-- Admin Link -->
              <!--  -->
            </div>
          </div>
          <!-- 
           -->
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div v-if="gameTitle" class="game-content">
      <!-- vif above solely for setting default currency - would love a better solution -->
      <router-view
        :game-session-id="routeSssnId"
        :current-property-index="currentPropertyIndex"
        :gameTitle="gameTitle"
        :gameDefaultCurrency="gameDefaultCurrency"
        :gameCommunitiesDetails="gameCommunitiesDetails"
        :realtyGameSummary="realtyGameSummary"
        :firstPropListing="firstPropListing"
        :totalProperties="totalProperties"
        :isCurrentUserSession="isCurrentUserSession"
        @update-progress="handleProgressUpdate"
        @game-complete="handleGameComplete"
      />
    </div>

    <!-- Footer -->
    <div class="game-footer">
      <div class="round-up-header-max-ctr">
        <div class="row items-center justify-between q-pa-md">
          <div class="footer-left">
            <!-- <q-btn flat
                   color="grey-7"
                   icon="home"
                   label="Home" /> -->
          </div>
          <div>
            <SocialSharing
              socialSharingPrompt=""
              socialSharingTitle=""
              urlProp=""
            ></SocialSharing>
          </div>
          <!-- <div>
            <QrCodeShare urlProp=""
                           qrCodeTitle=""></QrCodeShare>
          </div> -->
          <div class="footer-right">
            <!-- <q-btn v-if="canShare"
                   flat
                   color="primary"
                   icon="share"
                   label="Share"
                   @click="shareGame" /> -->
          </div>
        </div>
      </div>
    </div>
    <!-- <CookieConsent /> -->
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import { useRealtyGame } from 'src/concerns/realty-game/composables/useRealtyGame'
import { useRealtyGameStorage } from 'src/concerns/realty-game/composables/useRealtyGameStorage'
import useJsonLd from 'src/compose/useJsonLd.js'
import SocialSharing from 'src/concerns/dossiers/components/sharing/SocialSharing.vue'
import { useRealtyGameStore } from 'src/stores/realtyGame'

// Add logoUrl import (as in RoundupGameStartPage.vue)
import logoUrl from '/icons/favicon-128x128.png'

// Define props
const props = defineProps({
  isPriceGuessOnly: {
    type: Boolean,
    default: false,
  },
})

const $route = useRoute()
const $router = useRouter()
const $q = useQuasar()

const {
  totalProperties,
  gameTitle,
  realtyGameSummary,
  gameCommunitiesDetails,
  firstPropListing,
  fetchPriceGuessData,
  gameDesc,
  gameDefaultCurrency,
} = useRealtyGame()

// Initialize storage composable for shared computed values
const { getCurrentSessionId } = useRealtyGameStorage()

// Initialize JSON-LD functionality
const {
  initializeDefaultJsonLd,
  generateGameSessionSchema,
  addJsonLd,
  jsonLdMeta,
} = useJsonLd()

// Props from route
// const dossierUuid = computed(() => $route.params.dossierUuid)
const routeSssnId = computed(
  () => $route.query.session || $route.params.routeSssnId || ''
)

//
// const shareableResultsUrl = computed(() => {
//   if (!routeSssnId.value || !$route.params.gameSlug) {
//     return ''
//   }
//   let shareRoute = {
//     name: 'rRoundupGameResultsShareable',
//     params: {
//       routeSssnId: routeSssnId.value,
//       gameSlug: $route.params.gameSlug,
//     },
//   }
//   let fullPath = `${location.origin}${$router.resolve(shareRoute).href}`
//   return fullPath
// })

const isCurrentUserSession = computed(() => {
  const currentSessionId = getCurrentSessionId()
  return currentSessionId === routeSssnId.value
})

// Game state
const currentPropertyIndex = ref(null)
// const totalProperties = ref(null)

// Event handlers
const handleProgressUpdate = (data) => {
  currentPropertyIndex.value = data.currentIndex
  // totalProperties.value = data.total
}

const handleGameComplete = (sessionId) => {
  // Navigate to results page
  $router.push({
    name: 'rOneOffGameResults',
    params: {
      gameSlug: $route.params.gameSlug,
      routeSssnId: sessionId,
    },
  })
}

// const shareGame = () => {
//   const url = window.location.href
//   const text = `Check out my Property Price Challenge results!`

//   if (navigator.share) {
//     navigator
//       .share({
//         title: 'Property Price Challenge Results',
//         text: text,
//         url: url,
//       })
//       .catch((err) => {
//         console.log('Error sharing:', err)
//         fallbackShare(url, text)
//       })
//   } else {
//     fallbackShare(url, text)
//   }
// }

// const fallbackShare = (url, text) => {
//   if (navigator.clipboard) {
//     navigator.clipboard.writeText(url).then(() => {
//       $q.notify({
//         message: 'Results URL copied to clipboard!',
//         icon: 'content_copy',
//         color: 'positive',
//       })
//     })
//   } else {
//     $q.notify({
//       message: 'Share this URL: ' + url,
//       icon: 'share',
//       color: 'info',
//       timeout: 5000,
//     })
//   }
// }

const initializeGame = async () => {
  try {
    // await fetchPriceGuessData()
    await fetchPriceGuessData($route.params.gameSlug)
  } catch (err) {
    $q.notify({
      color: 'negative',
      message: 'Failed to load property data',
      icon: 'error',
    })
  }
}

// Initialize JSON-LD
initializeDefaultJsonLd()

// Add game-specific JSON-LD
const gameData = {
  title: gameTitle.value || 'Property Price Challenge',
  description:
    gameDesc.value ||
    'Test your property knowledge with our interactive price guessing game.',
  totalPlayers: 0, // This could be fetched from analytics
}
addJsonLd(generateGameSessionSchema(gameData), 'game-schema')

// Use the meta data with useMeta
// useMeta needs to be called AFTER JsonLd has been correctly created!!
// Only set meta tags if we're not on a property route (which handles its own meta tags)
// const isPropertyRoute = computed(() => $route.name === 'rOneOffGameProperty')

// const layoutMetaData = computed(() => {
//   // For property routes, don't set meta tags in layout - they're handled by preFetch
//   if (isPropertyRoute.value) {
//     // Only include JSON-LD scripts for property routes
//     return {
//       script: jsonLdMeta.value
//     }
//   }
//   // Otherwise return full meta data for non-property routes
//   return metaData.value
// })

// Initialize progress from route if available
onMounted(() => {
  if ($route.params.propertyIndex) {
    currentPropertyIndex.value = parseInt($route.params.propertyIndex)
  }

  initializeGame()
})
// Watch route changes to update progress
watch(
  () => $route.params.propertyIndex,
  (newIndex) => {
    if (newIndex !== undefined) {
      currentPropertyIndex.value = parseInt(newIndex)
    }
  }
)

// Add after other computed properties and before methods
const realtyGameStore = useRealtyGameStore()
const storeBgImageUrl = computed(() => realtyGameStore.gameBgImageUrl)
const storeGameTitle = computed(() => gameTitle.value)
const gameSlug = computed(() => $route.params.gameSlug)

const backgroundStyle = computed(() => {
  const baseStyle = {
    padding: '0px',
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat',
  }
  // Temp chapuzo hardcoding image
  const bgImage = "http://localhost:3333/staticfiles/hpg/holiday-destinations-bg-2.png" // storeBgImageUrl.value
  if (storeBgImageUrl.value) {
    return {
      ...baseStyle,
      backgroundImage: `url(${bgImage})`,
    }
  }
  // Fallback gradient
  const gradients = [
    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
  ]
  const index = gameSlug.value
    ? gameSlug.value.charCodeAt(0) % gradients.length
    : 0
  return { ...baseStyle, background: gradients[index] }
})

// Make sure to expose backgroundStyle and logoUrl to the template
// (script setup auto-exposes all top-level consts)

// Navigation handler for welcome card
const goToRoundupGameStart = () => {
  $router.push({
    name: 'rRoundupGameStart',
    params: { gameSlug: $route.params.gameSlug },
  })
}
</script>

<style scoped>
.roundup-game-layout {
  min-height: 100vh;
  /* background-color: #fafafa; */
  display: flex;
  flex-direction: column;
}

.round-up-header-max-ctr {
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.header-right {
  text-align: right;
}

.progress-info {
  margin-top: 0.5rem;
}

.game-content {
  flex: 1;
  overflow-y: auto;
}

.game-footer {
  background: white;
  border-top: 1px solid #e0e0e0;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .header-right {
    text-align: left;
    width: 100%;
  }

  .progress-info .q-linear-progress {
    width: 100% !important;
  }

  .hpg-logo-img {
    height: 40px;
    margin-bottom: 0.5rem;
  }

  .header-left {
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
  }
}

.hpg-logo-img {
  height: 60px;
  width: auto;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: #fff;
  transition: transform 0.2s;
}

.hpg-logo-img:hover {
  transform: scale(1.05) rotate(-2deg);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.18);
}

/* --- COPIED STYLES FROM RoundupGameStartPage.vue --- */
.welcome-section {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.game-overlay {
  position: relative;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    rgba(0, 0, 0, 0.7) 100%
  );
  padding: 32px;
  height: 100%;
  border-radius: 12px;
}

.welcome-icon {
  border-radius: 50%;
  width: 110px;
  height: 110px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.1);
  /* Subtle background for logo */
}

.hpg-logo-img {
  height: 70px;
  /* Slightly larger than the original 60px from PriceGuessGameLayout.vue */
  width: auto;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  /* Slightly stronger shadow for depth */
  background: #fff;
  padding: 8px;
  /* Padding to match the white background effect */
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hpg-logo-img:hover {
  transform: scale(1.1) rotate(-3deg);
  /* Slightly more pronounced hover effect */
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  /* Enhanced shadow on hover */
}

@media (max-width: 768px) {
  .welcome-section {
    padding: 0;
  }
  .game-overlay {
    padding: 24px;
    min-height: 400px;
  }
  .hpg-logo-img {
    height: 60px;
    /* Smaller size for mobile */
  }
  .welcome-icon {
    width: 100px;
    height: 100px;
  }
}

@media (max-width: 480px) {
  .game-overlay {
    padding: 20px;
    min-height: 450px;
  }
}
</style>
