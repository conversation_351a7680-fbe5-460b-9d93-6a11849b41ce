<template>
  <div class="realty-game-results-page">
    <div class="max-ctr q-pa-xs rgrp">
      <!-- Loading State -->
      <div v-if="isLoading"
           class="loading-container text-center q-pa-xl">
        <q-spinner color="primary"
                   size="3em" />
        <div class="q-mt-md text-h6">Loading Results...</div>
        <div class="text-body2 text-grey-7">
          Crunching the numbers and comparing scores
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error"
           class="error-container text-center q-pa-xl">
        <q-icon name="error"
                color="negative"
                size="3em" />
        <div class="q-mt-md text-h6 text-negative">Failed to Load Results</div>
        <div class="text-body2 text-grey-7 q-mb-lg">{{ error }}</div>
        <q-btn color="primary"
               label="Try Again"
               @click="loadResults" />
      </div>

      <!-- Results Content -->
      <div v-else-if="results">
        <RealtyGameResultsDetailedContent v-if="isCurrentUserSession"
                                          :results="results"
                                          :playerResults="playerResults"
                                          :ssGameSession="ssGameSession"
                                          :overallRanking="overallRanking"
                                          :leaderboard="leaderboard"
                                          :gameBreakdown="gameBreakdown"
                                          :comparisonSummary="comparisonSummary"
                                          :isCurrentUserSession="isCurrentUserSession"
                                          :showLeaderboard="showLeaderboard"
                                          :formatPriceWithBothCurrencies="formatPriceWithBothCurrencies"
                                          :getScoreColor="getScoreColor"
                                          :gameCommunitiesDetailsCalc="gameCommunitiesDetailsCalc"
                                          :shareableResultsUrl="shareableResultsUrl" />
        <div v-else
             class="q-mt-xl">
          <q-card class="access-restricted-card q-mb-xl"
                  flat
                  bordered>
            <q-card-section class="q-pa-lg text-center">
              <div class="text-h5 q-mb-sm text-weight-medium text-primary">
                <q-icon name="lock"
                        size="md"
                        class="q-mr-sm" />
                Restricted Access
              </div>
              <div class="text-subtitle1 text-grey-8 q-mb-md">
                You can view the summary results here though:
              </div>
              <q-btn :href="shareableResultsUrl"
                     target="_blank"
                     color="primary"
                     unelevated
                     no-caps
                     class="q-px-lg q-py-sm"
                     aria-label="View summary results">
                <q-icon name="link"
                        left
                        size="sm" />
                View Summary Results
              </q-btn>
            </q-card-section>
          </q-card>
        </div>
      </div>


      <!-- No Results -->
      <div v-else
           class="no-results-container text-center q-pa-xl">
        <q-icon name="search_off"
                color="grey-5"
                size="3em" />
        <div class="q-mt-md text-h6 text-grey-7">No Results Found</div>
        <div class="text-body2 text-grey-6 q-mb-lg">
          We couldn't find any results for this game session.
        </div>
        <q-btn color="primary"
               label="Start New Game"
               @click="startNewGame" />
      </div>
    </div>

    <!-- Feedback Popup Dialog -->
    <q-dialog v-model="showFeedbackDialog"
              :persistent="false"
              maximized
              transition-show="slide-up"
              transition-hide="slide-down"
              @hide="dismissFeedbackDialog">
      <q-card class="feedback-dialog-card">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">🎉 Thanks for Playing!</div>
          <q-space />
          <q-btn icon="close"
                 flat
                 round
                 dense
                 v-close-popup />
        </q-card-section>

        <q-card-section class="q-pt-none">
          <div class="text-body1 q-mb-lg">
            We'd love to hear your thoughts about the Property Price Challenge!
          </div>

          <!-- Feedback Form -->
          <q-form @submit="submitFeedback"
                  class="q-gutter-md">
            <!-- General Feedback -->
            <div>
              <q-input v-model="feedbackForm.generalFeedback"
                       type="textarea"
                       label="How was your experience? Any suggestions for improvement?"
                       outlined
                       rows="4"
                       hint="Optional - but we really appreciate your thoughts!" />
            </div>

            <!-- Create Own Game Section -->
            <q-separator class="q-my-lg" />

            <div class="create-game-section">
              <div class="text-h6 q-mb-md">
                🎮 Want to Create Your Own Game?
              </div>
              <div class="text-body2 q-mb-md text-grey-7">
                Create a custom property price guessing game for your area or
                community on propertysquares.com
              </div>

              <q-checkbox v-model="feedbackForm.wantsToCreateGame"
                          label="Yes, I'm interested in creating my own property price game!"
                          color="primary" />

              <!-- Show email/subdomain fields if interested -->
              <div v-if="feedbackForm.wantsToCreateGame"
                   class="q-mt-md q-gutter-md">
                <q-input v-model="feedbackForm.email"
                         type="email"
                         label="Your Email Address"
                         outlined
                         required
                         :rules="[
                          (val) => !!val || 'Email is required',
                          (val) =>
                            /.+@.+\..+/.test(val) || 'Please enter a valid email',
                        ]" />

                <q-input :model-value="feedbackForm.subdomain"
                         @update:model-value="
                          (val) => (feedbackForm.subdomain = val.toLowerCase())
                        "
                         label="Desired Subdomain"
                         outlined
                         required
                         prefix="https://"
                         suffix=".propertysquares.com"
                         hint="Choose a unique name for your game (e.g., 'mygame' for mygame.propertysquares.com)"
                         :rules="[
                          (val) => !!val || 'Subdomain is required',
                          (val) =>
                            /^[a-z0-9-]+$/.test(val) ||
                            'Only lowercase letters, numbers, and hyphens allowed',
                        ]" />

                <!-- <q-input v-model="feedbackForm.gameDescription"
                         type="textarea"
                         label="Describe your game idea"
                         outlined
                         rows="3"
                         hint="What area/properties would you like to feature? Any special requirements?" /> -->
              </div>
            </div>

            <!-- Submit Buttons -->
            <div class="row q-gutter-md q-mt-lg">
              <q-btn type="submit"
                     color="primary"
                     label="Submit Feedback"
                     :loading="feedbackLoading"
                     :disable="feedbackForm.wantsToCreateGame &&
                      (!feedbackForm.email || !feedbackForm.subdomain)
                      " />

              <q-btn label="Maybe Later"
                     color="grey"
                     flat
                     @click="dismissFeedbackDialog" />
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useQuasar } from 'quasar'
import axios from 'axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'
// Import the new composable
import { useServerRealtyGameResults } from '../composables/useServerRealtyGameResults'
import { useCurrencyConverter } from '../composables/useCurrencyConverter'
import { useRealtyGameStorage } from '../composables/useRealtyGameStorage'
import RealtyGameResultsDetailedContent from '../components/RealtyGameResultsDetailedContent.vue'
import { useRealtyGameMetaStore } from 'src/stores/realtyGameMeta'
import { useRealtyGameStore } from 'src/stores/realtyGame'

const props = defineProps({
  routeSssnId: {
    type: String,
    required: true,
  },
  gameCommunitiesDetails: {
    type: Object,
  },
  shareableResultsUrl: {
    type: String,
    required: true,
  },
  isCurrentUserSession: {
    type: Boolean,
    required: true,
  },
})

const $router = useRouter()
const $route = useRoute()
const $q = useQuasar()

// Initialize the new composable
const {
  isLoading,
  error,
  results,
  playerResults,
  comparisonSummary,
  gameBreakdown,
  ssGameSession,
  fetchResults,
  getScoreColor,
} = useServerRealtyGameResults()

// Initialize currency converter
const { setCurrency, formatPriceWithBothCurrencies } = useCurrencyConverter()

// Initialize storage to get currency selection
const { getCurrencySelection } = useRealtyGameStorage()

// shareableResultsUrl and isCurrentUserSession now come as props

// Set currency from session
const sessionCurrency = getCurrencySelection(props.routeSssnId)
if (sessionCurrency) {
  setCurrency(sessionCurrency)
}

// Feedback popup state
const showFeedbackDialog = ref(false)
const feedbackLoading = ref(false)
const feedbackForm = ref({
  generalFeedback: '',
  wantsToCreateGame: false,
  email: '',
  subdomain: '',
  gameDescription: '',
})

// LocalStorage keys for feedback popup dismissal and delay
const FEEDBACK_DISMISSED_KEY = 'realty_game_feedback_dismissed'
const FEEDBACK_DISMISS_COUNT_KEY = 'realty_game_feedback_dismiss_count'

// Add computed for leaderboard and overall ranking
const leaderboard = computed(() => results.value?.leaderboard || [])
const overallRanking = computed(() => results.value?.overall_ranking || null)

// Only show leaderboard if ?showLb is present in the query
const showLeaderboard = computed(() => !!$route.query.showLb)

// Computed properties for the component
const gameCommunitiesDetailsCalc = computed(() => {
  // This logic is client-specific and remains here
  if (typeof window !== 'undefined') {
    // const currentHost = location.host;
    // const hosts = ['nuneaton.propertysquares.com', 'brum.propertysquares.com', 'brum-houses.propertysquares.com'];
    // const relevantGames = hosts.filter(host => host !== currentHost);
    // let redC = {
    //   url: 'https://www.reddit.com/r/propertysquares/',
    //   text: 'Join the discussion on reddit!'
    // };
    // if (currentHost === 'nuneaton.propertysquares.com') {
    //   redC.url = "https://www.reddit.com/r/nuneaton/";
    // }
    let showComDetails = false
    if (props.gameCommunitiesDetails.redditCommunity) {
      showComDetails = true
    }
    if (!props.gameCommunitiesDetails.show) {
      // if the show is false from server, then we don't want to show the details
      showComDetails = false
    }
    return {
      show: showComDetails,
      redditCommunity: props.gameCommunitiesDetails.redditCommunity,
      relevantGames: props.gameCommunitiesDetails.relevantGames,
    }
  } else {
    return { show: false }
  }
})



// Methods
const loadResults = async () => {
  await fetchResults(
    props.routeSssnId,
    $router.currentRoute.value.params.gameSlug
  )
}

const startNewGame = () => {
  $router.push({ name: 'rPriceGuessStart' })
}

// Helper to get exponential delay (base: 35s, double each time, max 24h)
function getFeedbackDelay() {
  let count = 0
  if (typeof window !== 'undefined') {
    count = parseInt(
      localStorage.getItem(FEEDBACK_DISMISS_COUNT_KEY) || '0',
      10
    )
  }
  // base delay: 35s, double each time, max 24h
  const base = 35000
  const max = 24 * 60 * 60 * 1000 // 24 hours
  return Math.min(base * Math.pow(2, count), max)
}

// Feedback popup methods
const showFeedbackPopup = () => {
  // Check if feedback has been dismissed before
  if (typeof window !== 'undefined') {
    const dismissed = localStorage.getItem(FEEDBACK_DISMISSED_KEY)
    if (!dismissed) {
      showFeedbackDialog.value = true
    }
  }
}

const dismissFeedbackDialog = () => {
  showFeedbackDialog.value = false
  // Mark as dismissed in localStorage and increment dismiss count
  if (typeof window !== 'undefined') {
    localStorage.setItem(FEEDBACK_DISMISSED_KEY, 'true')
    let count = parseInt(
      localStorage.getItem(FEEDBACK_DISMISS_COUNT_KEY) || '0',
      10
    )
    localStorage.setItem(FEEDBACK_DISMISS_COUNT_KEY, String(count + 1))
  }
}

const submitFeedback = async () => {
  feedbackLoading.value = true

  try {
    const feedbackData = {
      game_session_id: props.routeSssnId,
      general_feedback: feedbackForm.value.generalFeedback,
      wants_to_create_game: feedbackForm.value.wantsToCreateGame,
      email: feedbackForm.value.email,
      subdomain: feedbackForm.value.subdomain,
      game_description: feedbackForm.value.gameDescription,
      submitted_at: new Date().toISOString(),
      user_agent: navigator.userAgent,
      page_url: window.location.href,
    }

    // Submit to backend API
    const apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/forms_ppsq/sp/price_game_followup`
    const response = await axios.post(apiUrl, {
      feedback: feedbackData,
    })

    console.log('Feedback submitted successfully:', response.data)

    // Show success message
    $q.notify({
      message: feedbackForm.value.wantsToCreateGame
        ? "Thank you! We'll contact you about creating your custom game."
        : 'Thank you for your feedback!',
      color: 'positive',
      icon: 'check_circle',
      position: 'top',
    })

    // Mark as dismissed and close dialog
    dismissFeedbackDialog()

    // Reset form
    feedbackForm.value = {
      generalFeedback: '',
      wantsToCreateGame: false,
      email: '',
      subdomain: '',
      gameDescription: '',
    }
  } catch (error) {
    console.error('Failed to submit feedback:', error)

    let errorMessage = 'Failed to submit feedback. Please try again.'

    // Provide more specific error messages
    if (error.response?.status === 404) {
      errorMessage =
        'Feedback service is temporarily unavailable. Please try again later.'
    } else if (error.response?.status === 422) {
      errorMessage = 'Please check your input and try again.'
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (!navigator.onLine) {
      errorMessage = 'Please check your internet connection and try again.'
    }

    $q.notify({
      message: errorMessage,
      color: 'negative',
      icon: 'error',
      position: 'top',
    })
  } finally {
    feedbackLoading.value = false
  }
}

// Timer for feedback popup
let feedbackTimer = null

// Initialize on mount
onMounted(() => {
  loadResults()
  // Set timer to show feedback popup after exponential delay
  let delay = getFeedbackDelay()
  feedbackTimer = setTimeout(() => {
    showFeedbackPopup()
  }, delay)
})

// Cleanup timer on unmount
onUnmounted(() => {
  if (feedbackTimer) {
    clearTimeout(feedbackTimer)
  }
})
</script>

<script>
// useRealtyGameMetaStore and useRealtyGameStore are already imported in <script setup>, so do not import again
// axios and pwbFlexConfig are already imported in <script setup>, so do not import again

// Helper function to fetch and process data, used by both preFetch and client-side logic
const fetchAndSetGameData = async (gameSlug, store) => {
  const response = await axios.get(
    `${pwbFlexConfig.dataApiBase}/api_public/v4/realty_game_summary/${gameSlug}`
  )

  if (response.data) {
    const gameData = response.data.price_guess_inputs
    const realtyGameDetails = response.data.realty_game_details
    const gameListings =
      gameData?.game_listings?.filter(
        (game) => game.listing_details.visible === true
      ) || []

    const storeData = {
      gameListings: gameListings,
      gameTitle: realtyGameDetails?.game_title || 'Property Price Challenge',
      gameDesc: realtyGameDetails?.game_description || '',
      gameBgImageUrl: realtyGameDetails?.game_bg_image_url || '',
      gameDefaultCurrency: gameData?.default_currency || 'GBP',
      totalProperties: gameListings.length,
      currentProperty: null,
      isDataLoaded: true, // Flag to indicate data is ready
    }

    store.setRealtyGameData(storeData)
    return storeData
  }
  return null
}

export default {
  async preFetch({ currentRoute, ssrContext }) {
    const gameSlug = currentRoute.params.gameSlug
    if (!gameSlug) return

    try {
      const store = useRealtyGameStore()
      const metaStore = useRealtyGameMetaStore()
      const gameData = await fetchAndSetGameData(gameSlug, store)
      // Set meta tags in the meta store
      console.log(`setting meta tags with ${gameData.gameTitle}`)
      // would be nice to have the game_player_nickname in the meta tags
      // be this is not available in prefetch ${ssGameSession.game_player_nickname}
      // could retrieve player_results.session_guest_name from the server
      // but the juice is not worth the squeeze...
      if (gameData) {
        metaStore.setMeta({
          title: `Property Price Challenge results for: ${gameData.gameTitle}`,
          description:
            gameData.gameDesc ||
            'See your results and compare your property price guessing skills with others!',
          image:
            gameData.gameBgImageUrl ||
            'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg',
          url: `https://yourdomain.com${currentRoute.fullPath}`,
          keywords: `property price game, real estate challenge, property valuation, house price quiz, market knowledge test, ${gameData.gameTitle || ''
            }`,
        })
      }
      if (ssrContext) {
        ssrContext.realtyGameData = gameData
      }
    } catch (error) {
      console.error('preFetch error for results page:', error)
      // fail silently for meta
    }
  },
}
</script>

<style scoped>
.realty-game-results-page {
  /* background-color: #fafafa; */
  min-height: 100vh;
}

.max-ctr {
  max-width: 1200px;
  margin: 0 auto;
}

.loading-container,
.error-container,
.no-results-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}



/* Button improvements */
.q-btn {
  text-transform: none;
  font-weight: 500;
}

/* Feedback Dialog Styles */
.feedback-dialog-card {
  max-width: 600px;
  margin: 0 auto;
  max-height: 90vh;
  overflow-y: auto;
}

.create-game-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  border-left: 4px solid #1976d2;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  /* .max-ctr {
    padding: 1rem;
  } */
}
.access-restricted-card {
  max-width: 500px;
  margin: 0 auto;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease-in-out;
}

.access-restricted-card:hover {
  transform: translateY(-4px);
}</style>
