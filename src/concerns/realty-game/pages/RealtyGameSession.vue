<template>
  <!-- 20 July 2025 - replaces by PriceGuessSession -->
  <div class="p-6 max-w-4xl mx-auto">
    <q-no-ssr>
      <h2 class="text-2xl font-bold mb-4">Realty Game Storage Manager</h2>

      <!-- Session Controls -->
      <q-card class="mb-6">
        <q-card-section>
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold">Current Session</h3>
              <p v-if="hasCurrentSession">Session ID: {{ currentSessionId }}</p>
              <p v-else class="text-gray-500">No active session</p>

              <div>Player Name: {{ playerName }}</div>
            </div>
            <div class="flex gap-2">
              <q-btn
                color="primary"
                label="Start New Session"
                @click="startNewSession"
                :disable="hasCurrentSession"
              />
              <q-btn
                color="negative"
                label="Clear Current Session"
                @click="clearCurrentSession"
                :disable="!hasCurrentSession"
              />
              <q-btn
                color="negative"
                outline
                label="Clear All Storage"
                @click="clearStorage"
              />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Session Stats -->
      <q-card class="mb-6" v-if="hasCurrentSession">
        <q-card-section>
          <h3 class="text-lg font-semibold mb-2">Session Statistics</h3>
          <div class="grid grid-cols-2 gap-4">
            <div>
              <p>
                <strong>Guess Count:</strong>
                {{ currentSessionStats.guessCount }}
              </p>
              <p>
                <strong>Total Score:</strong>
                {{ currentSessionStats.totalScore }}
              </p>
            </div>
            <div>
              <p>
                <strong>Average Score:</strong>
                {{ currentSessionStats.averageScore.toFixed(2) }}
              </p>
              <p>
                <strong>Valid Guesses:</strong>
                {{ currentSessionStats.validGuesses }}
              </p>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Guesses List -->
      <q-card class="mb-6" v-if="hasCurrentSession">
        <q-card-section>
          <h3 class="text-lg font-semibold mb-2">Current Session Guesses</h3>
          <q-table
            :rows="guessesList"
            :columns="guessColumns"
            row-key="propertyUuid"
            :pagination="{ rowsPerPage: 5 }"
            class="shadow-none"
          >
            <template v-slot:body-cell-actions="props">
              <q-td :props="props">
                <q-btn
                  flat
                  color="negative"
                  label="Remove"
                  @click="removeGuess(props.row.propertyUuid)"
                />
              </q-td>
            </template>
          </q-table>
        </q-card-section>
      </q-card>

      <!-- Add Guess Form -->
      <q-card class="mb-6" v-if="hasCurrentSession">
        <q-card-section>
          <h3 class="text-lg font-semibold mb-2">Add New Guess</h3>
          <div class="q-gutter-md">
            <q-input
              v-model="newGuess.propertyUuid"
              label="Property UUID"
              :rules="[(val) => !!val || 'Property UUID is required']"
            />
            <q-input
              v-model.number="newGuess.score"
              type="number"
              label="Score (optional)"
            />
            <q-btn color="primary" label="Save Guess" @click="saveNewGuess" />
          </div>
        </q-card-section>
      </q-card>

      <!-- Session Data -->
      <q-card v-if="hasCurrentSession">
        <q-card-section>
          <h3 class="text-lg font-semibold mb-2">Session Data</h3>
          <q-input
            v-model="sessionDataInput"
            type="textarea"
            label="Session Data (JSON)"
            rows="5"
            :rules="[(val) => isValidJson(val) || 'Invalid JSON format']"
          />
          <q-btn
            color="primary"
            label="Save Session Data"
            @click="saveSessionDataFromInput"
            class="mt-2"
          />
        </q-card-section>
      </q-card>
    </q-no-ssr>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useRealtyGameStorage } from '../composables/useRealtyGameStorage'
import { usePlayerName } from 'src/concerns/realty-game/composables/usePlayerName'

export default {
  name: 'RealtyGameSessionStorageManager',
  setup() {
    const {
      currentSessionId,
      sessionGuesses,
      sessionData,
      hasCurrentSession,
      currentSessionStats,
      saveGuess,
      getCurrentSessionGuesses,
      saveSessionData,
      clearStorage,
      clearCurrentSession,
      startNewSession,
    } = useRealtyGameStorage()

    const {
      playerName,
      isPlayerNameValid,
      ensurePlayerName,
      initializePlayerName,
    } = usePlayerName()
    // New guess form
    const newGuess = ref({
      propertyUuid: '',
      guessValue: null,
      score: null,
    })

    const getSessionData = () => {
      return sessionData.value[currentSessionId.value] || {}
    }
    // Session data input
    const sessionDataInput = ref(JSON.stringify(getSessionData(), null, 2))

    // Guess table columns
    const guessColumns = [
      {
        name: 'propertyUuid',
        label: 'Property UUID',
        field: 'propertyUuid',
        sortable: true,
      },
      {
        name: 'guessValue',
        label: 'Guess Value',
        field: 'guessValue',
        sortable: true,
      },
      { name: 'score', label: 'Score', field: 'score', sortable: true },
      {
        name: 'timestamp',
        label: 'Timestamp',
        field: (row) => new Date(row.timestamp).toLocaleString(),
        sortable: true,
      },
      { name: 'actions', label: 'Actions', field: 'actions' },
    ]

    // Computed guesses list for table
    const guessesList = computed(() => {
      const guesses = getCurrentSessionGuesses()
      return Object.entries(guesses).map(([propertyUuid, guess]) => ({
        propertyUuid,
        ...guess,
      }))
    })

    // Validate JSON
    const isValidJson = (str) => {
      try {
        JSON.parse(str)
        return true
      } catch {
        return false
      }
    }

    // Save new guess
    const saveNewGuess = () => {
      if (!newGuess.value.propertyUuid || newGuess.value.guessValue === null) {
        return
      }
      saveGuess(newGuess.value.propertyUuid, {
        guessValue: newGuess.value.guessValue,
        score: newGuess.value.score,
      })
      newGuess.value = { propertyUuid: '', guessValue: null, score: null }
    }

    // Remove guess
    const removeGuess = (propertyUuid) => {
      const sessionId = currentSessionId.value
      if (sessionGuesses.value[sessionId]) {
        delete sessionGuesses.value[sessionId][propertyUuid]
        localStorage.setItem(
          'price_guess_guesses',
          JSON.stringify(sessionGuesses.value)
        )
      }
    }

    // Save session data from input
    const saveSessionDataFromInput = () => {
      if (isValidJson(sessionDataInput.value)) {
        const parsedData = JSON.parse(sessionDataInput.value)
        saveSessionData(parsedData)
      }
    }

    // Watch session data changes

    const updateSessionDataInput = () => {
      sessionDataInput.value = JSON.stringify(getSessionData(), null, 2)
    }
    // watch(currentSessionId, updateSessionDataInput)
    // watch(sessionData, updateSessionDataInput, { deep: true })

    return {
      playerName,
      currentSessionId,
      hasCurrentSession,
      currentSessionStats,
      guessesList,
      guessColumns,
      newGuess,
      sessionDataInput,
      startNewSession,
      clearCurrentSession,
      clearStorage,
      saveNewGuess,
      removeGuess,
      saveSessionData,
      isValidJson,
      saveSessionDataFromInput,
    }
  },
}
</script>

<style scoped>
/* Quasar handles most styling, but add custom styles if needed */
</style>
