<template>
  <q-page class="q-pa-md">
    <q-card v-if="isLoading" class="q-pa-xl text-center">
      <q-spinner size="50px" color="primary" />
      <div class="q-mt-md">Loading property...</div>
    </q-card>
    <q-card v-else class="q-pa-lg" style="max-width: 900px; margin: 0 auto">
      <q-card-section>
        <div class="text-h5 q-mb-md">Edit Game Property</div>
        <q-form @submit.prevent="handleSave">
          <q-toggle v-model="form.visible_in_game" label="Visible in Game" />
          <q-input
            v-model.number="form.position_in_game"
            type="number"
            label="Position in Game"
            outlined
            class="q-mb-md"
          />
          <q-input
            v-model="form.gl_title_atr"
            label="Game Listing Title"
            outlined
            class="q-mb-md"
          />
          <q-input
            v-model="form.gl_description_atr"
            label="Game Listing Description"
            type="textarea"
            outlined
            class="q-mb-md"
          />
          <q-input
            v-model="form.gl_vicinity_atr"
            label="Game Listing Vicinity"
            outlined
            class="q-mb-md"
          />
          <q-input
            v-model="form.gl_image_url_atr"
            label="Game Listing Image URL"
            outlined
            class="q-mb-md"
            readonly
            hint="This will be automatically set to the first visible image"
          />

          <!-- Photo Management Section -->
          <div class="q-mb-lg">
            <div class="text-h6 q-mb-md">Photo Management</div>
            <div class="text-body2 q-mb-md text-grey-7">
              Drag and drop to reorder photos. Toggle visibility with the eye icon.
              The first visible photo will be used as the main image.
            </div>

            <draggable
              v-model="orderedPhotos"
              @end="onDragEnd"
              item-key="uuid"
              class="photo-list"
              :animation="200"
              group="photos"
              :disabled="false"
              ghost-class="ghost-photo"
              handle=".drag-handle"
            >
              <template #item="{ element, index }">
                <div class="photo-item q-mb-md" :class="{ 'photo-hidden': !element.visible }">
                  <q-card class="photo-card">
                    <div class="row no-wrap">
                      <!-- Drag Handle -->
                      <div class="col-auto flex items-center q-pa-sm drag-handle">
                        <q-icon name="drag_indicator" class="text-grey-6 cursor-move" />
                      </div>

                      <!-- Photo Preview -->
                      <div class="col-auto">
                        <q-img
                          :src="element.image_details?.url"
                          style="width: 120px; height: 80px"
                          fit="cover"
                          class="rounded-borders"
                        >
                          <template v-slot:error>
                            <div class="absolute-full flex flex-center bg-grey-3">
                              <q-icon name="broken_image" size="24px" color="grey-6" />
                            </div>
                          </template>
                        </q-img>
                      </div>

                      <!-- Photo Info -->
                      <div class="col q-pa-sm">
                        <div class="text-subtitle2">{{ element.photo_title || 'Untitled Photo' }}</div>
                        <div class="text-caption text-grey-6">UUID: {{ element.uuid }}</div>
                        <div class="text-caption" :class="element.visible ? 'text-positive' : 'text-negative'">
                          {{ element.visible ? 'Visible' : 'Hidden' }}
                        </div>
                        <div v-if="getFirstVisibleIndex() === index && element.visible" class="text-caption text-primary">
                          <q-icon name="star" size="16px" /> Main Image
                        </div>
                      </div>

                      <!-- Visibility Toggle -->
                      <div class="col-auto flex items-center q-pa-sm">
                        <q-btn
                          :icon="element.visible ? 'visibility' : 'visibility_off'"
                          :color="element.visible ? 'positive' : 'negative'"
                          flat
                          round
                          @click="togglePhotoVisibility(element)"
                          :title="element.visible ? 'Hide photo' : 'Show photo'"
                        />
                      </div>
                    </div>
                  </q-card>
                </div>
              </template>
            </draggable>
          </div>

          <div class="row q-gutter-sm q-mt-lg">
            <q-btn
              type="submit"
              color="primary"
              :loading="isSaving"
              label="Save Changes"
            />
            <q-btn flat color="secondary" label="Cancel" @click="goBack" />
          </div>
        </q-form>
        <q-banner v-if="error" class="q-mt-md bg-negative text-white">{{
          error
        }}</q-banner>
        <q-banner v-if="success" class="q-mt-md bg-positive text-white"
          >Changes saved!</q-banner
        >
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import axios from 'axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'
import draggable from 'vuedraggable'

const props = defineProps({
  listingInGameUuid: { type: String, required: true },
  routeSssnId: { type: String, required: false },
})

const $q = useQuasar()
const $router = useRouter()
const isLoading = ref(true)
const isSaving = ref(false)
const error = ref('')
const success = ref(false)
const form = reactive({
  visible_in_game: false,
  position_in_game: 1,
  visible_photo_uuids: [],
  ordered_photo_uuids: [],
  gl_vicinity_atr: '',
  gl_image_url_atr: '',
  gl_title_atr: '',
  gl_description_atr: '',
})

// New reactive data for photo management
const orderedPhotos = ref([])

const fetchProperty = async () => {
  isLoading.value = true
  error.value = ''
  try {
    // Use admin endpoint to get all attributes
    const apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/game_sale_listings/show_rgl/${props.listingInGameUuid}`
    const { data } = await axios.get(apiUrl)
    const prop = data.sale_listing || {}
    const rtl = data.realty_game_listing || {}

    form.visible_in_game = rtl.visible_in_game ?? rtl.visible ?? false
    form.position_in_game = rtl.position_in_game ?? 1
    form.visible_photo_uuids = rtl.visible_photo_uuids
      ? [...rtl.visible_photo_uuids]
      : []
    form.ordered_photo_uuids = rtl.ordered_photo_uuids
      ? [...rtl.ordered_photo_uuids]
      : []
    form.gl_vicinity_atr = rtl.gl_vicinity_atr || ''
    form.gl_image_url_atr = rtl.gl_image_url_atr || ''
    form.gl_title_atr = rtl.gl_title_atr || ''
    form.gl_description_atr = rtl.gl_description_atr || ''

    // Initialize photo management
    initializePhotoData(prop.sale_listing_pics || [])

    // Update form arrays to ensure synchronization
    updateFormArrays()
  } catch (err) {
    error.value = err.response?.data?.message || 'Failed to load property.'
  } finally {
    isLoading.value = false
  }
}

// Initialize photo data for drag and drop interface
const initializePhotoData = (saleListingPics) => {
  const photosWithVisibility = saleListingPics.map(pic => ({
    ...pic,
    visible: form.visible_photo_uuids.includes(pic.uuid)
  }))

  // Create ordered list based on ordered_photo_uuids or fallback to original order
  if (form.ordered_photo_uuids.length > 0) {
    const orderedMap = new Map()
    photosWithVisibility.forEach(photo => orderedMap.set(photo.uuid, photo))

    orderedPhotos.value = form.ordered_photo_uuids
      .map(uuid => orderedMap.get(uuid))
      .filter(Boolean) // Remove any undefined entries

    // Add any photos not in ordered_photo_uuids to the end
    const orderedUuids = new Set(form.ordered_photo_uuids)
    const remainingPhotos = photosWithVisibility.filter(photo => !orderedUuids.has(photo.uuid))
    orderedPhotos.value.push(...remainingPhotos)
  } else {
    orderedPhotos.value = [...photosWithVisibility]
  }
}

// Get index of first visible photo
const getFirstVisibleIndex = () => {
  return orderedPhotos.value.findIndex(photo => photo.visible)
}

// Toggle photo visibility
const togglePhotoVisibility = (photo) => {
  photo.visible = !photo.visible
  updateFormArrays()
}

// Handle drag end event
const onDragEnd = () => {
  updateFormArrays()
}

// Update form arrays based on current photo state
const updateFormArrays = () => {
  // Update ordered_photo_uuids with current order
  form.ordered_photo_uuids = orderedPhotos.value.map(photo => photo.uuid)

  // Update visible_photo_uuids with currently visible photos
  form.visible_photo_uuids = orderedPhotos.value
    .filter(photo => photo.visible)
    .map(photo => photo.uuid)

  // Update gl_image_url_atr with first visible photo
  const firstVisiblePhoto = orderedPhotos.value.find(photo => photo.visible)
  form.gl_image_url_atr = firstVisiblePhoto?.image_details?.url || ''
}

const handleSave = async () => {
  isSaving.value = true
  error.value = ''
  success.value = false
  try {
    // PATCH to the management API for updating game listing attributes
    const apiUrl = `${pwbFlexConfig.dataApiBase}/api_mgmt/v4/listing_in_game_mgmt/${props.listingInGameUuid}/update`
    await axios.patch(apiUrl, {
      rgl_data: {
        visible_in_game: form.visible_in_game,
        position_in_game: form.position_in_game,
        visible_photo_uuids: form.visible_photo_uuids,
        ordered_photo_uuids: form.ordered_photo_uuids,
        gl_vicinity_atr: form.gl_vicinity_atr,
        gl_image_url_atr: form.gl_image_url_atr,
        gl_title_atr: form.gl_title_atr,
        gl_description_atr: form.gl_description_atr,
      },
    })
    success.value = true
    $q.notify({ color: 'positive', message: 'Changes saved!' })
    // setTimeout(() => goBack(), 1000)
  } catch (err) {
    error.value = err.response?.data?.message || 'Failed to save changes.'
    $q.notify({ color: 'negative', message: error.value })
  } finally {
    isSaving.value = false
  }
}

const goBack = () => {
  $router.push({
    name: 'rRoundupGameProperty',
    params: { listingInGameUuid: props.listingInGameUuid },
    query: { session: props.routeSssnId },
  })
}

onMounted(fetchProperty)
</script>

<style scoped>
.photo-list {
  min-height: 100px;
}

.photo-item {
  transition: all 0.3s ease;
}

.photo-hidden {
  opacity: 0.6;
}

.photo-card {
  border: 2px solid transparent;
  transition: border-color 0.2s ease;
}

.photo-card:hover {
  border-color: #1976d2;
}

.drag-handle {
  cursor: move;
}

.drag-handle:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.ghost-photo {
  opacity: 0.5;
  background: #f5f5f5;
}

.sortable-chosen {
  transform: rotate(5deg);
}

.sortable-drag {
  transform: rotate(5deg);
  opacity: 0.8;
}
</style>
